"use client";

import { useEffect, useState } from "react";
import { timeslotToLocalTime, timeslotToUTCDate } from "@/lib/time";
import { Order, ORDER_STATUSES, OrderStatus } from "@/model/order";
import OrderQR from "@/app/components/order/OrderQR";
import { Loading } from "@/app/components/Loading";
import Button from "@/app/components/Button";
import ErrorMessage from "@/app/components/ErrorMessage";
import { useTranslations } from "next-intl";
import { formatDate } from "date-fns";
import ItemCustomizationDisplay from "@/app/components/order/ItemCustomizationDisplay";

export default function ClientOrderPage({
  orderNumber,
}: {
  orderNumber: string;
}) {
  const [error, setError] = useState<string | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const t = useTranslations();

  // Fetch order data
  const fetchOrder = () => {
    fetch(`/api/order/${orderNumber}`)
      .then(async (response) => {
        const data = await response.json();
        if (!response.ok) {
          const error = data?.message ?? response.statusText;
          throw new Error(error);
        }
        return data;
      })
      .then((order) => {
        setOrder({ ...order });
        console.log("order11111111111", order);
        setIsLoading(false);
      })
      .catch((error) => {
        setError(error.message || "An error occurred");
        setIsLoading(false);
      });
  };

  useEffect(() => {
    fetchOrder();
  }, [orderNumber]);

  useEffect(() => {
    const interval = setInterval(fetchOrder, 5000);
    return () => clearInterval(interval);
  }, []);

  // Rest of your component logic
  const cancelOrder = () => {
    if (!order) {
      return;
    }

    fetch(`/api/order/${orderNumber}/cancel`, {
      method: "PUT",
      credentials: "include",
    })
      .then((response) => {
        if (response.ok) {
          setOrder({
            ...order,
            status: "cancelled" as OrderStatus,
          });
        } else {
          throw new Error("Failed to cancel order");
        }
      })
      .catch((error) => {
        setError(error.message);
      });
  };

  const hasComment = () => {
    if (!order) {
      return false;
    }

    return (
      typeof order.comment === "string" &&
      order.comment !== "" &&
      order.comment.toLowerCase() !== "no comment"
    );
  };

  const statusToText = (order: { status: OrderStatus }) =>
    t(`order_status.status.${order.status}`) ??
    t("order_status.status.default");
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case ORDER_STATUSES.ORDERED:
        return "bg-primary-100 text-primary-800";
      case ORDER_STATUSES.ACTIVE:
        return "bg-orange-100 text-orange-800";
      case ORDER_STATUSES.READY_FOR_PICKUP:
        return "bg-green-100 text-green-800";
      case ORDER_STATUSES.COMPLETED:
        return "bg-gray-100 text-gray-800";
      case ORDER_STATUSES.CANCELLED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return <Loading message="Loading Order..." />;
  }
  if (!order) {
    return (
      <div className="p-6 text-center">
        <h1 className="text-xl">{t("order.not_found")}</h1>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-md mx-auto rounded-2xl bg-white shadow-xl">
      {/* Status Hero Section */}
      <div className="px-6 py-12 text-center">
        <h1
          className={`text-2xl px-4 py-2 font-semibold mb-6 text-gray-900 rounded-full max-w-sm mx-auto ${getStatusColor(
            order.status
          )}`}
        >
          {statusToText(order)}
        </h1>

        {order.status !== "cancelled" && (
          <div className="bg-blue-50 rounded-2xl p-6 mb-6 max-w-sm mx-auto">
            <p className="text-sm text-gray-600 mb-1">
              {t("order_status.status.ready_by")}
            </p>
            <p className="text-3xl font-light text-blue-600">
              {timeslotToLocalTime(
                formatDate(timeslotToUTCDate(order.timeslot), "HH:mm")
              )}
            </p>
            <p className="text-sm text-gray-600">
              {formatDate(timeslotToUTCDate(order.timeslot), "dd.MM.yyyy")}
            </p>
          </div>
        )}
      </div>

      <div className="px-6 pb-6">
        <div className="max-w-sm mx-auto space-y-4">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-500">
                {t("order_status.order")} #{orderNumber.slice(-8)}
              </span>
              <span
                className={`text-sm font-medium ${
                  order.isPaid ? "text-green-600" : "text-red-600"
                }`}
              >
                {order.isPaid
                  ? t("order_status.status.paid")
                  : t("order_status.status.unpaid")}
              </span>
            </div>

            {/* Order Summary Information */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-3">
                📋 Order Summary
              </div>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <span className="text-gray-500">Customer:</span>
                  <div className="font-medium text-gray-900">{order.name}</div>
                </div>
                <div>
                  <span className="text-gray-500">Items:</span>
                  <div className="font-medium text-gray-900">
                    {order.items?.length || 0}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Order Time:</span>
                  <div className="font-medium text-gray-900">
                    {(order as any).createdAt
                      ? formatDate(
                          new Date((order as any).createdAt),
                          "dd.MM.yyyy HH:mm"
                        )
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Pickup Time:</span>
                  <div className="font-medium text-gray-900">
                    {timeslotToLocalTime(
                      formatDate(timeslotToUTCDate(order.timeslot), "HH:mm")
                    )}
                  </div>
                </div>
              </div>
              {hasComment() && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <span className="text-gray-500 text-xs">
                    Special Instructions:
                  </span>
                  <div className="font-medium text-gray-900 text-sm mt-1">
                    {order.comment}
                  </div>
                </div>
              )}
            </div>

            {/* Customization Options Overview */}
            {order?.items?.some(
              (item) =>
                item.item.customizable && item.item.customization_options
            ) && (
              <div className="mb-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border-2 border-purple-200">
                <div className="text-base font-bold text-purple-900 mb-3 flex items-center gap-2">
                  �️ CUSTOMIZATION OPTIONS AVAILABLE
                </div>
                <div className="text-sm text-purple-800 mb-3">
                  These items have customization options available:
                </div>
                <div className="space-y-2">
                  {order.items
                    .filter(
                      (item) =>
                        item.item.customizable &&
                        item.item.customization_options
                    )
                    .map((item, index) => {
                      const customizationOptions =
                        item.item.customization_options;
                      return (
                        <div
                          key={index}
                          className="bg-white p-3 rounded-lg border border-purple-100"
                        >
                          <div className="font-bold text-purple-900 mb-2">
                            {item.item.name}
                          </div>
                          <div className="flex flex-wrap gap-2 text-xs">
                            {customizationOptions?.size_options &&
                              customizationOptions.size_options.length > 0 && (
                                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                                  Sizes:{" "}
                                  {customizationOptions.size_options
                                    .map((s) => s.size)
                                    .join(", ")}
                                </span>
                              )}
                            {customizationOptions?.additional_toppings &&
                              customizationOptions.additional_toppings.length >
                                0 && (
                                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                                  Toppings:{" "}
                                  {
                                    customizationOptions.additional_toppings
                                      .length
                                  }{" "}
                                  available
                                </span>
                              )}
                            {customizationOptions?.removable_ingredients &&
                              customizationOptions.removable_ingredients
                                .length > 0 && (
                                <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full font-medium">
                                  Removable:{" "}
                                  {
                                    customizationOptions.removable_ingredients
                                      .length
                                  }{" "}
                                  ingredients
                                </span>
                              )}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            <div className="space-y-4">
              {order?.items?.map((item, index) => {
                const hasCustomizations = !!(item.item as any)
                  .customization_details;
                const customizationDetails = (item.item as any)
                  .customization_details;

                return (
                  <div
                    key={`${item.item._id.toString()}-${index}`}
                    className="border-b border-gray-100 pb-4 last:border-b-0"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-gray-900 font-medium">
                            {item.item.name}
                          </span>
                          {hasCustomizations && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">
                              ✅ CUSTOMIZED
                            </span>
                          )}
                        </div>

                        {/* Item Status */}
                        <div className="mb-2">
                          <span
                            className={`text-xs px-2 py-1 rounded-full font-medium ${
                              item.status === "prepping"
                                ? "bg-orange-100 text-orange-800"
                                : item.status === "readyToCook"
                                ? "bg-yellow-100 text-yellow-800"
                                : item.status === "cooking"
                                ? "bg-red-100 text-red-800"
                                : item.status === "ready"
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {item.status === "prepping"
                              ? "Preparing"
                              : item.status === "readyToCook"
                              ? "Ready to Cook"
                              : item.status === "cooking"
                              ? "Cooking"
                              : item.status === "ready"
                              ? "Ready"
                              : item.status}
                          </span>
                        </div>

                        {/* Enhanced Customization Display */}
                        <ItemCustomizationDisplay
                          item={item.item}
                          showTitle={hasCustomizations}
                          compact={false}
                        />

                        {/* Customer's Actual Customizations */}
                        {customizationDetails && (
                          <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border-2 border-green-200 shadow-sm">
                            <div className="text-base font-bold text-green-900 mb-4 flex items-center gap-2">
                              ✅ YOUR CUSTOMIZATIONS
                            </div>
                            <div className="space-y-3">
                              {/* Size Selection */}
                              {customizationDetails.size && (
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium text-green-700 bg-green-100 px-2 py-1 rounded-full">
                                    Size:
                                  </span>
                                  <span className="font-bold text-green-900">
                                    {customizationDetails.size.toUpperCase()}
                                  </span>
                                </div>
                              )}

                              {/* Added Toppings */}
                              {customizationDetails.added_toppings &&
                                Object.keys(customizationDetails.added_toppings)
                                  .length > 0 && (
                                  <div>
                                    <span className="text-sm font-medium text-green-700 bg-green-100 px-2 py-1 rounded-full">
                                      Added Toppings:
                                    </span>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                      {Object.entries(
                                        customizationDetails.added_toppings
                                      ).map(([topping, quantity]) => (
                                        <span
                                          key={topping}
                                          className="bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm font-medium"
                                        >
                                          {topping}
                                          {Number(quantity) > 1
                                            ? ` (x${Number(quantity)})`
                                            : ""}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}

                              {/* Removed Ingredients */}
                              {customizationDetails.removed_ingredients &&
                                customizationDetails.removed_ingredients
                                  .length > 0 && (
                                  <div>
                                    <span className="text-sm font-medium text-red-700 bg-red-100 px-2 py-1 rounded-full">
                                      Removed Ingredients:
                                    </span>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                      {customizationDetails.removed_ingredients.map(
                                        (ingredient: string) => (
                                          <span
                                            key={ingredient}
                                            className="bg-red-200 text-red-800 px-3 py-1 rounded-full text-sm font-medium line-through"
                                          >
                                            {ingredient}
                                          </span>
                                        )
                                      )}
                                    </div>
                                  </div>
                                )}
                            </div>
                          </div>
                        )}

                        {/* Base Item Information */}
                        {item.item.ingredients &&
                          item.item.ingredients.length > 0 && (
                            <div className="mt-2 p-2 bg-gray-50 rounded-md">
                              <div className="text-xs font-medium text-gray-700 mb-1">
                                Base Ingredients:
                              </div>
                              <div className="text-xs text-gray-600">
                                {item.item.ingredients
                                  .map((ing) => ing.name)
                                  .join(", ")}
                              </div>
                            </div>
                          )}
                      </div>
                      <span className="text-gray-600 font-semibold ml-3">
                        €
                        {(typeof item.item.price === "object"
                          ? item.item.price.amount
                          : item.item.price
                        ).toFixed(2)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="border-t border-gray-200 mt-4 pt-4 flex justify-between text-lg font-semibold">
              <span>{t("order_status.total")}</span>
              <span>
                €
                {order?.items
                  ?.reduce((total, item) => {
                    const price =
                      typeof item.item.price === "object"
                        ? item.item.price.amount
                        : item.item.price;
                    return total + price;
                  }, 0)
                  .toFixed(2)}
              </span>
            </div>
          </div>

          {/* QR Code Card */}
          <div className="p-6 text-center">
            <p className="text-sm text-gray-600 mb-4">
              {t("order_status.show_at_pickup")}
            </p>
            <div className="flex justify-center">
              <OrderQR orderId={orderNumber} />
            </div>
          </div>

          {error && (
            <div className="p-6 text-center">
              <ErrorMessage error={error} />
            </div>
          )}

          {/* Cancel Button */}
          {order.status !== "cancelled" && (
            <Button
              onClick={cancelOrder}
              className="w-full py-4 text-red-600 bg-white border border-red-200 rounded-2xl font-medium hover:bg-red-50 transition-colors"
            >
              {t("order_status.cancel_order")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
