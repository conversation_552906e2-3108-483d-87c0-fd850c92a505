import { ItemModel } from "@/model/item";
import { CategoryModel } from "@/model/category";
import { AllergenModel } from "@/model/allergen";
import dbConnect from "@/lib/dbConnect";
import { NextResponse } from "next/server";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function GET(request: Request) {
    return withDB(async () => {
        // Get all categories, items, and allergens
        const [categories, items, allergens] = await Promise.all([
            CategoryModel.find({ enabled: true }).sort({ sort_order: 1 }).lean(),
            ItemModel.find({ enabled: true }).sort({ sort_order: 1, name: 1 }).lean(),
            AllergenModel.find({ enabled: true }).sort({ id: 1 }).lean()
        ]);

        // Group items by category
        const categoriesWithItems = categories.map(category => ({
            ...category,
            items: items.filter(item => item.category_id === category.id)
        }));

        // Build the complete menu structure similar to Quick Pizza
        const menuStructure = {
            restaurant: {
                name: "TueTops Pizza",
                description: "Delicious pizza delivery and restaurant",
                cuisine_type: ["Italian", "Pizza", "Pasta"]
            },
            allergens: allergens.reduce((acc, allergen) => {
                acc[allergen.id] = {
                    name: allergen.name,
                    description: allergen.description
                };
                return acc;
            }, {} as Record<string, any>),
            menu: {
                categories: categoriesWithItems
            }
        };

        return NextResponse.json(menuStructure);
    }).catch((err) => {
        console.error('GET /menu error ➜', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}
