import { ItemDocument } from "@/model/item";
import { CategoryDocument } from "@/model/category";
import { AllergenDocument } from "@/model/allergen";
import { useQuery } from "@tanstack/react-query";

// Enhanced menu structure interface
export interface MenuStructure {
    restaurant: {
        name: string;
        description: string;
        cuisine_type: string[];
    };
    allergens: Record<string, {
        name: string;
        description: string;
    }>;
    menu: {
        categories: (CategoryDocument & { items: ItemDocument[] })[];
    };
}

const getItems = async (params?: {
    category?: string;
    type?: string;
    dietary?: string;
    allergen_free?: string;
}): Promise<ItemDocument[]> => {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.type) searchParams.set('type', params.type);
    if (params?.dietary) searchParams.set('dietary', params.dietary);
    if (params?.allergen_free) searchParams.set('allergen_free', params.allergen_free);

    const response = await fetch(`/api/pizza?${searchParams.toString()}`, {
        method: 'GET',
        credentials: 'include',
    });
    return await response.json();
};

const getMenu = async (): Promise<MenuStructure> => {
    const response = await fetch(`/api/menu`, {
        method: 'GET',
        credentials: 'include',
    });
    return await response.json();
};

const getCategories = async (): Promise<CategoryDocument[]> => {
    const response = await fetch(`/api/categories`, {
        method: 'GET',
        credentials: 'include',
    });
    return await response.json();
};

const getAllergens = async (): Promise<AllergenDocument[]> => {
    const response = await fetch(`/api/allergens`, {
        method: 'GET',
        credentials: 'include',
    });
    return await response.json();
};

export function useItems(
    params?: {
        category?: string;
        type?: string;
        dietary?: string;
        allergen_free?: string;
    },
    refetchInterval: number | false = false
) {
    return useQuery({
        queryKey: ['items', params],
        queryFn: () => getItems(params),
        refetchInterval: refetchInterval
    });
}

export function useMenu(refetchInterval: number | false = false) {
    return useQuery({
        queryKey: ['menu'],
        queryFn: () => getMenu(),
        refetchInterval: refetchInterval
    });
}

export function useCategories(refetchInterval: number | false = false) {
    return useQuery({
        queryKey: ['categories'],
        queryFn: () => getCategories(),
        refetchInterval: refetchInterval
    });
}

export function useAllergens(refetchInterval: number | false = false) {
    return useQuery({
        queryKey: ['allergens'],
        queryFn: () => getAllergens(),
        refetchInterval: refetchInterval
    });
}
