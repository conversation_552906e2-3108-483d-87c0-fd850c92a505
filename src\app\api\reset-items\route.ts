import { ItemModel } from "@/model/item";
import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";
import { seedAllData } from "@/lib/seedData";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

// Sample items with the new structure
const sampleItems = [
    // DOP Pizzas
    {
        name: "Margherita DOP",
        description: "San Marzano tomatoes, Mozzarella di Bufala Campana DOP, fresh basil, extra virgin olive oil",
        price: { amount: 18.50, currency: "CHF" },
        category_id: "dop_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: true,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "San Marzano tomatoes", category: "sauce" },
            { name: "Mozzarella di Bufala Campana DOP", category: "cheese", origin: "Italy" },
            { name: "Fresh basil", category: "herb" },
            { name: "Extra virgin olive oil", category: "sauce" }
        ],
        allergens: ["1", "2"], // Gluten, Milk
        size: 1,
        enabled: true,
        customizable: false,
        premium: true,
        signature_dish: true,
        sort_order: 1
    },
    {
        name: "Marinara DOP",
        description: "San Marzano tomatoes, garlic, oregano, extra virgin olive oil",
        price: { amount: 16.00, currency: "CHF" },
        category_id: "dop_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: false,
            dairy_free: true
        },
        ingredients: [
            { name: "San Marzano tomatoes", category: "sauce" },
            { name: "Garlic", category: "herb" },
            { name: "Oregano", category: "herb" },
            { name: "Extra virgin olive oil", category: "sauce" }
        ],
        allergens: ["1"], // Gluten
        size: 1,
        enabled: true,
        customizable: false,
        premium: true,
        signature_dish: true,
        sort_order: 2
    },

    // Classic Pizzas
    {
        name: "Quattro Stagioni",
        description: "Tomato sauce, mozzarella, ham, mushrooms, artichokes, olives",
        price: { amount: 22.50, currency: "CHF" },
        category_id: "classic_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: false,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "Tomato sauce", category: "sauce" },
            { name: "Mozzarella", category: "cheese" },
            { name: "Ham", category: "meat" },
            { name: "Mushrooms", category: "vegetable" },
            { name: "Artichokes", category: "vegetable" },
            { name: "Olives", category: "vegetable" }
        ],
        allergens: ["1", "2"], // Gluten, Milk
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Extra cheese", "Pepperoni", "Bell peppers"],
            removable_ingredients: ["Ham", "Mushrooms", "Artichokes", "Olives"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 1
    },
    {
        name: "Diavola",
        description: "Tomato sauce, mozzarella, spicy salami, chili peppers",
        price: { amount: 20.00, currency: "CHF" },
        category_id: "classic_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: false,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "Tomato sauce", category: "sauce" },
            { name: "Mozzarella", category: "cheese" },
            { name: "Spicy salami", category: "meat" },
            { name: "Chili peppers", category: "vegetable" }
        ],
        allergens: ["1", "2"], // Gluten, Milk
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Extra cheese", "Extra salami", "Jalapeños"],
            removable_ingredients: ["Chili peppers"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 2
    },
    {
        name: "Capricciosa",
        description: "Tomato sauce, mozzarella, ham, mushrooms, artichokes, olives, eggs",
        price: { amount: 23.00, currency: "CHF" },
        category_id: "classic_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: false,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "Tomato sauce", category: "sauce" },
            { name: "Mozzarella", category: "cheese" },
            { name: "Ham", category: "meat" },
            { name: "Mushrooms", category: "vegetable" },
            { name: "Artichokes", category: "vegetable" },
            { name: "Olives", category: "vegetable" },
            { name: "Eggs", category: "specialty" }
        ],
        allergens: ["1", "2", "3"], // Gluten, Milk, Eggs
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Extra cheese", "Bell peppers"],
            removable_ingredients: ["Ham", "Mushrooms", "Artichokes", "Olives", "Eggs"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 3
    },

    // Vegan Pizzas
    {
        name: "Vegan Margherita",
        description: "Tomato sauce, vegan mozzarella, fresh basil, olive oil",
        price: { amount: 19.50, currency: "CHF" },
        category_id: "vegan_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: false,
            dairy_free: true
        },
        ingredients: [
            { name: "Tomato sauce", category: "sauce" },
            { name: "Vegan mozzarella", category: "vegan_cheese" },
            { name: "Fresh basil", category: "herb" },
            { name: "Olive oil", category: "sauce" }
        ],
        allergens: ["1"], // Gluten
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Vegan pepperoni", "Bell peppers", "Mushrooms"],
            removable_ingredients: ["Fresh basil"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 1
    },
    {
        name: "Vegan Vegetale",
        description: "Tomato sauce, vegan cheese, bell peppers, zucchini, eggplant, mushrooms",
        price: { amount: 21.50, currency: "CHF" },
        category_id: "vegan_pizzas",
        type: "pizza",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: false,
            dairy_free: true
        },
        ingredients: [
            { name: "Tomato sauce", category: "sauce" },
            { name: "Vegan cheese", category: "vegan_cheese" },
            { name: "Bell peppers", category: "vegetable" },
            { name: "Zucchini", category: "vegetable" },
            { name: "Eggplant", category: "vegetable" },
            { name: "Mushrooms", category: "vegetable" }
        ],
        allergens: ["1"], // Gluten
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Vegan pepperoni", "Olives", "Artichokes"],
            removable_ingredients: ["Bell peppers", "Zucchini", "Eggplant", "Mushrooms"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 2
    },

    // Pasta
    {
        name: "Spaghetti Carbonara",
        description: "Spaghetti with eggs, pecorino cheese, pancetta, black pepper",
        price: { amount: 16.50, currency: "CHF" },
        category_id: "pasta",
        type: "pasta",
        dietary: {
            vegetarian: false,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "Spaghetti", category: "specialty" },
            { name: "Eggs", category: "specialty" },
            { name: "Pecorino cheese", category: "cheese" },
            { name: "Pancetta", category: "meat" },
            { name: "Black pepper", category: "herb" }
        ],
        allergens: ["1", "2", "3"], // Gluten, Milk, Eggs
        size: 1,
        enabled: true,
        customizable: false,
        premium: false,
        signature_dish: true,
        sort_order: 1
    },
    {
        name: "Penne Arrabbiata",
        description: "Penne pasta with spicy tomato sauce, garlic, chili peppers, parsley",
        price: { amount: 14.50, currency: "CHF" },
        category_id: "pasta",
        type: "pasta",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: false,
            dairy_free: true
        },
        ingredients: [
            { name: "Penne pasta", category: "specialty" },
            { name: "Spicy tomato sauce", category: "sauce" },
            { name: "Garlic", category: "herb" },
            { name: "Chili peppers", category: "vegetable" },
            { name: "Parsley", category: "herb" }
        ],
        allergens: ["1"], // Gluten
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Parmesan cheese", "Extra chili"],
            removable_ingredients: ["Chili peppers"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 2
    },

    // Salads
    {
        name: "Caesar Salad",
        description: "Romaine lettuce, parmesan, croutons, Caesar dressing",
        price: { amount: 12.50, currency: "CHF" },
        category_id: "salads",
        type: "salad",
        dietary: {
            vegetarian: true,
            vegan: false,
            gluten_free: false,
            dairy_free: false
        },
        ingredients: [
            { name: "Romaine lettuce", category: "vegetable" },
            { name: "Parmesan cheese", category: "cheese" },
            { name: "Croutons", category: "specialty" },
            { name: "Caesar dressing", category: "sauce" }
        ],
        allergens: ["1", "2", "3"], // Gluten, Milk, Eggs
        size: 1,
        enabled: true,
        customizable: true,
        customization_options: {
            additional_toppings: ["Grilled chicken", "Anchovies"],
            removable_ingredients: ["Croutons", "Parmesan cheese"]
        },
        premium: false,
        signature_dish: false,
        sort_order: 1
    },

    // Beverages
    {
        name: "Coca Cola",
        description: "Classic Coca Cola 33cl",
        price: { amount: 3.50, currency: "CHF" },
        category_id: "beverages",
        type: "beverage",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: true,
            dairy_free: true
        },
        ingredients: [],
        allergens: [],
        size: 0.33,
        enabled: true,
        customizable: false,
        premium: false,
        signature_dish: false,
        sort_order: 1
    },
    {
        name: "Sparkling Water",
        description: "San Pellegrino sparkling water 50cl",
        price: { amount: 4.00, currency: "CHF" },
        category_id: "beverages",
        type: "beverage",
        dietary: {
            vegetarian: true,
            vegan: true,
            gluten_free: true,
            dairy_free: true
        },
        ingredients: [],
        allergens: [],
        size: 0.5,
        enabled: true,
        customizable: false,
        premium: false,
        signature_dish: false,
        sort_order: 2
    }
];

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        
        console.log('Starting item reset and seeding...');
        
        // First, seed categories and allergens
        await seedAllData();
        
        // Clear all existing items
        const deletedCount = await ItemModel.deleteMany({});
        console.log(`Deleted ${deletedCount.deletedCount} existing items`);
        
        // Insert new sample items
        const insertedItems = await ItemModel.insertMany(sampleItems);
        console.log(`Inserted ${insertedItems.length} new items`);
        
        return NextResponse.json({ 
            message: 'Items reset and seeded successfully',
            deletedItems: deletedCount.deletedCount,
            insertedItems: insertedItems.length,
            timestamp: new Date().toISOString()
        });
    }).catch((err) => {
        console.error('Error resetting items:', err);
        return NextResponse.json(
            { message: 'There was an error resetting items', error: err.message },
            { status: 500 },
        );
    });
}
