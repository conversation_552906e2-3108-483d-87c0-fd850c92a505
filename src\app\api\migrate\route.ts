import { ItemModel } from "@/model/item";
import { CategoryModel } from "@/model/category";
import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";
import { seedAllData } from "@/lib/seedData";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        
        console.log('Starting data migration...');
        
        // First, seed categories and allergens
        await seedAllData();
        
        // Get all existing items
        const existingItems = await ItemModel.find({}).lean();
        console.log(`Found ${existingItems.length} existing items to migrate`);
        
        // Migrate each item to new format
        for (const item of existingItems) {
            try {
                // Determine category based on type
                let categoryId = 'classic_pizzas'; // default
                if (item.type === 'pizza') {
                    if (item.dietary === 'vegan') {
                        categoryId = 'vegan_pizzas';
                    } else {
                        categoryId = 'classic_pizzas';
                    }
                } else if (item.type === 'pasta') {
                    categoryId = 'pasta';
                } else if (item.type === 'salad') {
                    categoryId = 'salads';
                } else if (item.type === 'beverage') {
                    categoryId = 'beverages';
                }

                // Convert dietary string to object
                const dietaryInfo = {
                    vegetarian: item.dietary === 'vegetarian' || item.dietary === 'vegan',
                    vegan: item.dietary === 'vegan',
                    gluten_free: item.dietary?.includes('gluten') || false,
                    dairy_free: item.dietary === 'vegan' || false
                };

                // Convert ingredients to structured format
                const structuredIngredients = (item.ingredients || []).map((ing: string) => ({
                    name: ing,
                    category: 'vegetable' as const, // default category
                }));

                // Convert price to structured format
                const priceStructure = {
                    amount: typeof item.price === 'number' ? item.price : 0,
                    currency: 'CHF'
                };

                // Basic allergen detection (simplified)
                const allergens: string[] = [];
                if (structuredIngredients.some(ing => ing.name.toLowerCase().includes('cheese') || ing.name.toLowerCase().includes('mozzarella'))) {
                    allergens.push('2'); // Milk
                }
                allergens.push('1'); // Gluten (most pizzas have gluten)

                // Update the item
                await ItemModel.updateOne(
                    { _id: item._id },
                    {
                        $set: {
                            category_id: categoryId,
                            dietary: dietaryInfo,
                            ingredients: structuredIngredients,
                            price: priceStructure,
                            allergens: allergens,
                            customizable: true, // Make most items customizable
                            premium: false,
                            signature_dish: false,
                            sort_order: 0
                        }
                    }
                );
                
                console.log(`Migrated item: ${item.name}`);
            } catch (error) {
                console.error(`Error migrating item ${item.name}:`, error);
            }
        }
        
        return NextResponse.json({ 
            message: 'Data migration completed successfully',
            migratedItems: existingItems.length,
            timestamp: new Date().toISOString()
        });
    }).catch((err) => {
        console.error('Error during migration:', err);
        return NextResponse.json(
            { message: 'There was an error during migration', error: err.message },
            { status: 500 },
        );
    });
}
