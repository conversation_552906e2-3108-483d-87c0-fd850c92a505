import { AllergenModel } from "@/model/allergen";
import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function GET(request: Request) {
    return withDB(async () => {
        const allergens = await AllergenModel.find({ enabled: true })
            .sort({ id: 1 })
            .lean();
        return NextResponse.json(allergens);
    }).catch((err) => {
        console.error('GET /allergens error ➜', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        const newAllergen = await request.json();
        console.log('Creating allergen with data:', newAllergen);
        
        const allergen = new AllergenModel(newAllergen);
        await allergen.save();
        return NextResponse.json(allergen);
    }).catch((err) => {
        console.error('Error creating allergen:', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}
