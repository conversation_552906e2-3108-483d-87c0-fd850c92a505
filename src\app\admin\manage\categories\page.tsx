'use client'

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useCategories } from '@/lib/fetch/item';
import { CategoryDocument } from '@/model/category';
import { Loading } from '@/app/components/Loading';
import ErrorMessage from '@/app/components/ErrorMessage';
import { Heading } from '@/app/components/layout/Heading';
import Button from '@/app/components/Button';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';

const CategoriesPage = () => {
    const [editingCategory, setEditingCategory] = useState<CategoryDocument | null>(null);
    const [isCreating, setIsCreating] = useState(false);
    const [formData, setFormData] = useState({
        id: '',
        name: '',
        description: '',
        note: '',
        sort_order: 0
    });

    const t = useTranslations();
    const { data: categories, error, isFetching, refetch } = useCategories();

    const handleEdit = (category: CategoryDocument) => {
        setEditingCategory(category);
        setFormData({
            id: category.id,
            name: category.name,
            description: category.description || '',
            note: category.note || '',
            sort_order: category.sort_order
        });
    };

    const handleCreate = () => {
        setIsCreating(true);
        setFormData({
            id: '',
            name: '',
            description: '',
            note: '',
            sort_order: 0
        });
    };

    const handleSave = async () => {
        try {
            const response = await fetch('/api/categories', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                refetch();
                setEditingCategory(null);
                setIsCreating(false);
            } else {
                console.error('Failed to save category');
            }
        } catch (error) {
            console.error('Error saving category:', error);
        }
    };

    const handleCancel = () => {
        setEditingCategory(null);
        setIsCreating(false);
        setFormData({
            id: '',
            name: '',
            description: '',
            note: '',
            sort_order: 0
        });
    };

    if (isFetching) {
        return <Loading message="Loading categories..." />;
    }

    if (error) {
        return <ErrorMessage error={error.message} />;
    }

    return (
        <div className="space-y-6">
            <Heading 
                title="Manage Categories" 
                description="Organize your menu items into categories"
                icon={<Edit className="w-10 h-10 text-gray-900" />}
            />

            {/* Create New Category */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Categories</h2>
                    <Button
                        onClick={handleCreate}
                        className="bg-primary-600 hover:bg-primary-700 text-white"
                    >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Category
                    </Button>
                </div>

                {/* Create/Edit Form */}
                {(isCreating || editingCategory) && (
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="font-medium mb-4">
                            {isCreating ? 'Create New Category' : 'Edit Category'}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    ID
                                </label>
                                <input
                                    type="text"
                                    value={formData.id}
                                    onChange={(e) => setFormData({ ...formData, id: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="category_id"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Name
                                </label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="Category Name"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Description
                                </label>
                                <input
                                    type="text"
                                    value={formData.description}
                                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="Category description"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Sort Order
                                </label>
                                <input
                                    type="number"
                                    value={formData.sort_order}
                                    onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Note
                                </label>
                                <input
                                    type="text"
                                    value={formData.note}
                                    onChange={(e) => setFormData({ ...formData, note: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="Special note (optional)"
                                />
                            </div>
                        </div>
                        <div className="flex gap-2 mt-4">
                            <Button
                                onClick={handleSave}
                                className="bg-green-600 hover:bg-green-700 text-white"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                Save
                            </Button>
                            <Button
                                onClick={handleCancel}
                                className="bg-gray-500 hover:bg-gray-600 text-white"
                            >
                                <X className="w-4 h-4 mr-2" />
                                Cancel
                            </Button>
                        </div>
                    </div>
                )}

                {/* Categories List */}
                <div className="space-y-3">
                    {categories?.map((category) => (
                        <div
                            key={category._id.toString()}
                            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                        >
                            <div className="flex-1">
                                <div className="flex items-center gap-3">
                                    <span className="font-medium">{category.name}</span>
                                    <span className="text-sm text-gray-500">({category.id})</span>
                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                        Order: {category.sort_order}
                                    </span>
                                </div>
                                {category.description && (
                                    <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                                )}
                                {category.note && (
                                    <p className="text-xs text-orange-600 mt-1">{category.note}</p>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    onClick={() => handleEdit(category)}
                                    className="bg-blue-500 hover:bg-blue-600 text-white"
                                >
                                    <Edit className="w-4 h-4" />
                                </Button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CategoriesPage;
