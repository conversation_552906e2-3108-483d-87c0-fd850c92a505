'use client'

import React, { useState, useMemo } from 'react';
import { ItemDocument, SizeOption } from '@/model/item';
import { X, Plus, Minus } from 'lucide-react';
import Button from '@/app/components/Button';
import { useTranslations } from 'next-intl';

interface PizzaCustomizerProps {
    item: ItemDocument;
    isOpen: boolean;
    onClose: () => void;
    onAddToCart: (customizedItem: ItemDocument, quantity: number) => void;
}

// Default customization options based on Quick Pizza data
const defaultToppings = [
    { category: 'basic', price: 2.0, items: ['Olives', 'Garlic', 'Onions', 'Chili', 'Basil'] },
    { category: 'standard', price: 3.0, items: ['Egg', 'Mushrooms', 'Capers', 'Bell Peppers'] },
    { category: 'premium', price: 4.0, items: ['Tuna', 'Anchovies', 'Bacon', 'Ham', 'Cheese', 'Arugula'] },
    { category: 'specialty_cheese', price: 5.0, items: ['Buffalo Mozzarella', 'Vegan Cheese', 'Planted Protein'] },
    { category: 'gourmet', price: 8.0, items: ['Nduja', 'Chicken', 'Fish', 'Mortadella', 'Burrata'] },
    { category: 'premium_meat', price: 10.0, items: ['San Daniele Ham (80g)', 'Grilled Sausage'] },
    { category: 'luxury', price: 12.0, items: ['Beef Strips (120g)', 'Black Truffle'] }
];

const defaultSizes: SizeOption[] = [
    { size: 'small', price: -2.0, description: 'Small (2.- cheaper)' },
    { size: 'regular', price: 0.0, description: 'Regular size' },
    { size: 'large', price: 8.0, description: 'Large pizza for 2 people' }
];

const PizzaCustomizer = ({ item, isOpen, onClose, onAddToCart }: PizzaCustomizerProps) => {
    const [selectedSize, setSelectedSize] = useState<string>('regular');
    const [selectedToppings, setSelectedToppings] = useState<Record<string, number>>({});
    const [removedIngredients, setRemovedIngredients] = useState<Set<string>>(new Set());
    const [quantity, setQuantity] = useState(1);
    
    const t = useTranslations();

    const sizeOptions = item.customization_options?.size_options || defaultSizes;
    const availableToppings = defaultToppings; // Could be customized per item

    const totalPrice = useMemo(() => {
        const basePrice = item.price?.amount || (item.price as any) || 0;
        const sizePrice = sizeOptions.find(s => s.size === selectedSize)?.price || 0;
        const toppingsPrice = Object.entries(selectedToppings).reduce((total, [topping, qty]) => {
            const toppingData = availableToppings.find(cat => 
                cat.items.includes(topping)
            );
            return total + (toppingData?.price || 0) * qty;
        }, 0);
        
        return (basePrice + sizePrice + toppingsPrice) * quantity;
    }, [item.price, selectedSize, selectedToppings, quantity, sizeOptions, availableToppings]);

    const handleToppingChange = (topping: string, change: number) => {
        setSelectedToppings(prev => {
            const current = prev[topping] || 0;
            const newValue = Math.max(0, current + change);
            if (newValue === 0) {
                const { [topping]: removed, ...rest } = prev;
                return rest;
            }
            return { ...prev, [topping]: newValue };
        });
    };

    const toggleIngredientRemoval = (ingredient: string) => {
        setRemovedIngredients(prev => {
            const newSet = new Set(prev);
            if (newSet.has(ingredient)) {
                newSet.delete(ingredient);
            } else {
                newSet.add(ingredient);
            }
            return newSet;
        });
    };

    const handleAddToCart = () => {
        // Create customized item
        const customizedItem: ItemDocument = {
            ...item,
            name: `${item.name} (${selectedSize})`,
            price: {
                amount: totalPrice / quantity,
                currency: item.price?.currency || 'CHF'
            },
            // Store customization in a way that can be processed
            customization_details: {
                size: selectedSize,
                added_toppings: selectedToppings,
                removed_ingredients: Array.from(removedIngredients)
            }
        } as any;

        // 🔍 CONSOLE LOG: Customization data being created
        console.log('🍕 PIZZA CUSTOMIZER - Creating customized item:');
        console.log('==================================================');
        console.log('📋 Base item:', {
            id: item._id,
            name: item.name,
            basePrice: item.price,
            ingredients: item.ingredients?.map(ing => ing.name)
        });
        console.log('🎛️ Customizations selected:', {
            size: selectedSize,
            added_toppings: selectedToppings,
            removed_ingredients: Array.from(removedIngredients),
            quantity: quantity
        });
        console.log('💰 Price calculation:', {
            basePrice: item.price?.amount || (item.price as any) || 0,
            totalPrice: totalPrice,
            pricePerItem: totalPrice / quantity
        });
        console.log('📦 Final customized item:', {
            id: customizedItem._id,
            name: customizedItem.name,
            price: customizedItem.price,
            customization_details: customizedItem.customization_details
        });
        console.log('==================================================\n');

        onAddToCart(customizedItem, quantity);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-gray-900">Customize {item.name}</h2>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                        <X className="w-6 h-6" />
                    </button>
                </div>

                <div className="p-6 space-y-6">
                    {/* Pizza Base Ingredients */}
                    {item.ingredients && item.ingredients.length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold mb-3">Base Ingredients</h3>
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <p className="text-sm text-gray-600 mb-3">Your {item.name} comes with:</p>
                                <div className="flex flex-wrap gap-2">
                                    {item.ingredients.map((ingredient) => (
                                        <span
                                            key={ingredient.name}
                                            className={`text-xs px-3 py-1 rounded-full border transition-colors ${
                                                removedIngredients.has(ingredient.name)
                                                    ? 'bg-red-100 text-red-800 border-red-200 line-through'
                                                    : 'bg-blue-100 text-blue-800 border-blue-200'
                                            }`}
                                        >
                                            {ingredient.name}
                                        </span>
                                    ))}
                                </div>
                                {removedIngredients.size > 0 && (
                                    <p className="text-xs text-red-600 mt-2">
                                        ✗ Crossed out ingredients will be removed
                                    </p>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Size Selection */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Size</h3>
                        <div className="grid grid-cols-1 gap-2">
                            {sizeOptions.map((size) => (
                                <label
                                    key={size.size}
                                    className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                                        selectedSize === size.size
                                            ? 'border-primary-500 bg-primary-50'
                                            : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                >
                                    <div className="flex items-center">
                                        <input
                                            type="radio"
                                            name="size"
                                            value={size.size}
                                            checked={selectedSize === size.size}
                                            onChange={(e) => setSelectedSize(e.target.value)}
                                            className="mr-3"
                                        />
                                        <div>
                                            <div className="font-medium capitalize">{size.size}</div>
                                            {size.description && (
                                                <div className="text-sm text-gray-500">{size.description}</div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="font-medium">
                                        {size.price > 0 ? `+${size.price.toFixed(2)}` : 
                                         size.price < 0 ? `${size.price.toFixed(2)}` : 'included'}
                                    </div>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Remove Ingredients */}
                    {item.ingredients && item.ingredients.length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold mb-3">Customize Base Ingredients</h3>
                            <p className="text-sm text-gray-600 mb-3">
                                Click to remove ingredients you don't want:
                            </p>
                            <div className="grid grid-cols-2 gap-2">
                                {item.ingredients.map((ingredient) => (
                                    <label
                                        key={ingredient.name}
                                        className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                                            removedIngredients.has(ingredient.name)
                                                ? 'border-red-300 bg-red-50'
                                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                        }`}
                                    >
                                        <input
                                            type="checkbox"
                                            checked={removedIngredients.has(ingredient.name)}
                                            onChange={() => toggleIngredientRemoval(ingredient.name)}
                                            className="mr-3"
                                        />
                                        <span className={`text-sm ${
                                            removedIngredients.has(ingredient.name)
                                                ? 'line-through text-red-600'
                                                : ''
                                        }`}>
                                            {ingredient.name}
                                        </span>
                                        {removedIngredients.has(ingredient.name) && (
                                            <span className="ml-auto text-xs text-red-600">✗ Remove</span>
                                        )}
                                    </label>
                                ))}
                            </div>
                            {removedIngredients.size > 0 && (
                                <div className="mt-3 p-3 bg-red-50 rounded-lg border border-red-200">
                                    <p className="text-sm text-red-700">
                                        <strong>Removing:</strong> {Array.from(removedIngredients).join(', ')}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Currently Added Toppings */}
                    {Object.keys(selectedToppings).length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold mb-3">Added Toppings</h3>
                            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                <div className="flex flex-wrap gap-2">
                                    {Object.entries(selectedToppings).map(([topping, quantity]) => (
                                        <div
                                            key={topping}
                                            className="flex items-center gap-2 bg-green-100 text-green-800 px-3 py-2 rounded-full border border-green-300"
                                        >
                                            <span className="text-sm font-medium">
                                                {topping} {quantity > 1 ? `(×${quantity})` : ''}
                                            </span>
                                            <button
                                                onClick={() => handleToppingChange(topping, -1)}
                                                className="p-0.5 hover:bg-green-200 rounded-full transition-colors"
                                                title="Remove one"
                                            >
                                                <Minus className="w-3 h-3" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                                <p className="text-xs text-green-700 mt-2">
                                    ✓ These toppings will be added to your pizza
                                </p>
                            </div>
                        </div>
                    )}

                    {/* Add Toppings */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Add Extra Toppings</h3>
                        {availableToppings.map((category) => (
                            <div key={category.category} className="mb-4">
                                <h4 className="font-medium text-gray-700 mb-2 capitalize">
                                    {category.category.replace('_', ' ')} (+{category.price.toFixed(2)} CHF each)
                                </h4>
                                <div className="grid grid-cols-1 gap-2">
                                    {category.items.map((topping) => {
                                        const isSelected = selectedToppings[topping] > 0;
                                        return (
                                            <div
                                                key={topping}
                                                className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
                                                    isSelected
                                                        ? 'border-green-300 bg-green-50'
                                                        : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                            >
                                                <span className={`text-sm ${isSelected ? 'font-medium text-green-800' : ''}`}>
                                                    {topping}
                                                </span>
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        onClick={() => handleToppingChange(topping, -1)}
                                                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                                                        disabled={!selectedToppings[topping]}
                                                    >
                                                        <Minus className="w-4 h-4" />
                                                    </button>
                                                    <span className="w-8 text-center font-medium">
                                                        {selectedToppings[topping] || 0}
                                                    </span>
                                                    <button
                                                        onClick={() => handleToppingChange(topping, 1)}
                                                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                                                    >
                                                        <Plus className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Quantity */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Quantity</h3>
                        <div className="flex items-center gap-4">
                            <button
                                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                                className="p-2 border border-gray-300 rounded hover:bg-gray-50"
                            >
                                <Minus className="w-4 h-4" />
                            </button>
                            <span className="text-xl font-medium w-12 text-center">{quantity}</span>
                            <button
                                onClick={() => setQuantity(quantity + 1)}
                                className="p-2 border border-gray-300 rounded hover:bg-gray-50"
                            >
                                <Plus className="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                </div>

                {/* Customization Summary */}
                {(selectedSize !== 'regular' || Object.keys(selectedToppings).length > 0 || removedIngredients.size > 0) && (
                    <div className="border-t border-gray-200 p-6 bg-gray-50">
                        <h3 className="text-lg font-semibold mb-3">Your Customization Summary</h3>
                        <div className="space-y-2">
                            {selectedSize !== 'regular' && (
                                <div className="flex items-center gap-2">
                                    <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                        Size: {selectedSize}
                                    </span>
                                </div>
                            )}

                            {Object.keys(selectedToppings).length > 0 && (
                                <div>
                                    <p className="text-sm font-medium text-green-700 mb-1">Added Toppings:</p>
                                    <div className="flex flex-wrap gap-1">
                                        {Object.entries(selectedToppings).map(([topping, qty]) => (
                                            <span key={topping} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                                {topping} {qty > 1 ? `(×${qty})` : ''}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {removedIngredients.size > 0 && (
                                <div>
                                    <p className="text-sm font-medium text-red-700 mb-1">Removed Ingredients:</p>
                                    <div className="flex flex-wrap gap-1">
                                        {Array.from(removedIngredients).map((ingredient) => (
                                            <span key={ingredient} className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                                {ingredient}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Footer */}
                <div className="sticky bottom-0 bg-white border-t border-gray-200 p-6">
                    <div className="flex items-center justify-between mb-4">
                        <span className="text-lg font-semibold">Total:</span>
                        <span className="text-2xl font-bold text-primary-600">
                            {totalPrice.toFixed(2)} {item.price?.currency || 'CHF'}
                        </span>
                    </div>
                    <Button
                        onClick={handleAddToCart}
                        className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 text-lg font-medium"
                    >
                        Add to Cart
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default PizzaCustomizer;
