import { Document, model, Model, Schema, Types } from "mongoose";

export interface Allergen {
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    createdAt?: Date;
}

// Extend the interface to include the MongoDB `_id` field
export interface AllergenDocument extends Allergen, Document {
    _id: Types.ObjectId;
}

// Define the schema for the Allergen model
export const allergenSchema = new Schema<AllergenDocument>(
    {
        id: { type: String, required: true, unique: true },
        name: { type: String, required: true },
        description: { type: String, required: true },
        enabled: { type: Boolean, default: true },
        createdAt: { type: Date, default: Date.now },
    },
    {
        timestamps: true,
    }
);

allergenSchema.index({ id: 1 });

// Create the Allergen model
let AllergenModel: Model<AllergenDocument>;
try {
    AllergenModel = model<AllergenDocument>("Allergen");
} catch (error) {
    AllergenModel = model<AllergenDocument>("Allergen", allergenSchema);
}

export { AllergenModel };
