import { ItemModel } from "@/model/item";
import { CategoryModel } from "@/model/category";
import { AllergenModel } from "@/model/allergen";
import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function GET(request: Request) {
    return withDB(async () => {
        const url = new URL(request.url);
        const category = url.searchParams.get('category');
        const type = url.searchParams.get('type');
        const dietary = url.searchParams.get('dietary');
        const allergen_free = url.searchParams.get('allergen_free');

        // Build query
        let query: any = { enabled: true };

        if (category) {
            query.category_id = category;
        }

        if (type) {
            query.type = type;
        }

        if (dietary) {
            switch (dietary) {
                case 'vegetarian':
                    query['dietary.vegetarian'] = true;
                    break;
                case 'vegan':
                    query['dietary.vegan'] = true;
                    break;
                case 'gluten_free':
                    query['dietary.gluten_free'] = true;
                    break;
                case 'dairy_free':
                    query['dietary.dairy_free'] = true;
                    break;
            }
        }

        if (allergen_free) {
            // Exclude items that contain the specified allergen
            query.allergens = { $ne: allergen_free };
        }

        const items = await ItemModel.find(query)
            .sort({ sort_order: 1, name: 1 })
            .lean();

        return NextResponse.json(items);
    }).catch((err) => {
        console.error('GET /pizza error ➜', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        const newPizza = await request.json()
        console.log('Creating pizza with data:', newPizza);
        const pizza = new ItemModel(newPizza);
        await pizza.save();
        return NextResponse.json(pizza);
    }).catch((err) => {
        console.error('Error creating pizza:', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    })
}

export async function PUT(request: Request) {
    return withDB(async () => {
        await requireAuth();
        const item = await request.json()
        const updatedItem = await ItemModel.findById(item._id);
        if (!updatedItem) {
            return NextResponse.json({
                message: 'Pizza not found'
            }, { status: 404 });
        }

        updatedItem.set(item);
        await updatedItem.save();
        return Response.json(updatedItem);
    }).catch((err) => {
        console.error('Error updating pizza:', err);
        return NextResponse.json({
            message: 'Error updating pizza'
        }, { status: 500 });
    })
}
