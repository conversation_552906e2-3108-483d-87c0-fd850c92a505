'use client'

import { ItemDocument } from '@/model/item'
import useOrderStore from '@/app/zustand/order'
import { Plus, Star, Leaf, Award, Settings } from 'lucide-react'
import React, { useState } from 'react'
import Button from "@/app/components/Button";
import { useTranslations } from "next-intl";
import { useShallow } from "zustand/react/shallow";
import { useAllergens } from '@/lib/fetch/item';
import PizzaCustomizer from './PizzaCustomizer';

interface EnhancedItemProps {
    item: ItemDocument
}

export const EnhancedItem = ({ item }: EnhancedItemProps) => {
    const { data: allergens } = useAllergens();
    const [showCustomizer, setShowCustomizer] = useState(false);

    return (
        <>
            <article className="bg-white border border-gray-100 rounded-xl hover:shadow-md transition-all duration-200 overflow-hidden">
                <div className="p-6">
                    <div className="flex items-start justify-between gap-4">
                        <ItemDetails item={item} allergens={allergens} />
                        <ItemActions
                            item={item}
                            onCustomize={() => setShowCustomizer(true)}
                        />
                    </div>
                </div>
            </article>

            {item.customizable && (
                <PizzaCustomizer
                    item={item}
                    isOpen={showCustomizer}
                    onClose={() => setShowCustomizer(false)}
                    onAddToCart={(customizedItem, quantity) => {
                        // Handle adding customized item to cart
                        const { addToOrder } = useOrderStore.getState();
                        for (let i = 0; i < quantity; i++) {
                            addToOrder(customizedItem);
                        }
                        setShowCustomizer(false);
                    }}
                />
            )}
        </>
    )
}

interface ItemDetailsProps {
    item: ItemDocument;
    allergens?: any;
}

const ItemDetails = ({ item, allergens }: ItemDetailsProps) => {
    const t = useTranslations();
    const hasIngredients = (item.ingredients && item.ingredients.length > 0) ?? false;

    return (
        <div className="flex-1 min-w-0">
            {/* Header with badges */}
            <div className="flex items-start gap-2 mb-2">
                <h3 className="text-lg font-bold text-gray-900 flex-1">
                    {item.name}
                </h3>
                <div className="flex gap-1">
                    {item.premium && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                            <Star className="w-3 h-3" />
                            Premium
                        </span>
                    )}
                    {item.signature_dish && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                            <Award className="w-3 h-3" />
                            Signature
                        </span>
                    )}
                    {item.dietary?.vegan && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            <Leaf className="w-3 h-3" />
                            Vegan
                        </span>
                    )}
                    {item.dietary?.vegetarian && !item.dietary?.vegan && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            <Leaf className="w-3 h-3" />
                            Vegetarian
                        </span>
                    )}
                </div>
            </div>

            {/* Description */}
            {item.description && (
                <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                    {item.description}
                </p>
            )}

            {/* Ingredients */}
            {hasIngredients && (
                <div className="mb-3">
                    <p className="text-sm text-gray-700 leading-relaxed">
                        <span className="font-medium">Ingredients: </span>
                        {item.ingredients?.map(ing => ing.name).join(', ')}
                    </p>
                </div>
            )}

            {/* Specialty ingredient */}
            {item.specialty_ingredient && (
                <div className="mb-3">
                    <p className="text-sm text-orange-600 font-medium">
                        ✨ Features: {item.specialty_ingredient}
                    </p>
                </div>
            )}

            {/* Dietary info */}
            <div className="flex flex-wrap gap-2 mb-3">
                {item.dietary?.gluten_free && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        Gluten Free
                    </span>
                )}
                {item.dietary?.dairy_free && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        Dairy Free
                    </span>
                )}
            </div>

            {/* Allergen info */}
            {item.allergens && item.allergens.length > 0 && allergens && (
                <div className="mb-3">
                    <p className="text-xs text-gray-500">
                        <span className="font-medium">Contains allergens: </span>
                        {item.allergens.map(id => allergens[id]?.name).filter(Boolean).join(', ')}
                    </p>
                </div>
            )}

            {/* Price */}
            <div className="flex items-baseline gap-2">
                <span className="text-xl font-bold text-gray-900">
                    {item.price?.amount?.toFixed(2) || item.price?.toFixed(2)}{item.price?.currency || t('cart.currency')}
                </span>
                {item.customizable && (
                    <span className="text-xs text-gray-500">
                        + customizable
                    </span>
                )}
            </div>
        </div>
    )
};

interface ItemActionsProps {
    item: ItemDocument;
    onCustomize?: () => void;
}

const ItemActions = ({ item, onCustomize }: ItemActionsProps) => {
    const { addToOrder, itemCount } = useOrderStore(
        useShallow((state) => ({
            addToOrder: state.addToOrder,
            itemCount: state.getItemCount(item._id.toString()),
        }))
    )
    const isInCart = itemCount > 0

    const handleAddItem = () => {
        if (item.customizable && onCustomize) {
            onCustomize();
        } else {
            addToOrder(item);
        }
    }

    return (
        <div className="shrink-0 flex flex-col items-center gap-2">
            {item.customizable && onCustomize && (
                <Button
                    onClick={onCustomize}
                    className="mb-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300"
                >
                    <Settings className="w-4 h-4 mr-1" />
                    Customize
                </Button>
            )}
            <AddButton
                isInCart={isInCart}
                itemCount={itemCount}
                itemName={item.name}
                itemPrice={item.price?.amount || (item.price as any) || 0}
                onAddItem={handleAddItem}
                customizable={item.customizable}
            />
        </div>
    )
};

interface AddButtonProps {
    isInCart: boolean
    itemCount: number
    itemName: string
    itemPrice: number
    onAddItem: () => void
    customizable?: boolean
}

const AddButton = ({
    isInCart,
    itemCount,
    itemName,
    itemPrice,
    onAddItem,
    customizable
}: AddButtonProps) => {
    const baseClasses = "group relative overflow-hidden transition-all duration-200 ease-in-out transform hover:scale-105 active:scale-95"
    const variantClasses = isInCart
        ? "bg-primary-600 hover:bg-primary-700 text-white shadow-lg"
        : "bg-white hover:bg-primary-50 text-primary-600 border-2 border-primary-600 hover:border-primary-700"

    const t = useTranslations();

    return (
        <Button
            onClick={onAddItem}
            className={`${baseClasses} ${variantClasses}`}
            aria-label={`Add ${itemName} to cart`}
        >
            <div className="flex items-center justify-center min-w-[80px] px-4 py-2">
                <div className="flex flex-col items-center">
                    <span className="text-sm font-medium">
                        {t('order.item.button.add')} {itemPrice.toFixed(2)}{t('cart.currency')}
                    </span>
                    {customizable && (
                        <span className="text-xs opacity-75">
                            Customize
                        </span>
                    )}
                </div>
                {isInCart && (
                    <div className="relative ml-2">
                        <Plus className="transition-transform group-hover:rotate-90"/>
                        <span className="absolute -top-2 -right-4 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                            {itemCount}
                        </span>
                    </div>
                )}
            </div>
        </Button>
    )
}

export default EnhancedItem
