'use client'

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useMenu } from '@/lib/fetch/item';
import { ItemDocument } from '@/model/item';
import { CategoryDocument } from '@/model/category';
import Item from "@/app/components/order/Item";
import EnhancedItem from "@/app/components/order/EnhancedItem";
import { Loading } from "@/app/components/Loading";
import ErrorMessage from "@/app/components/ErrorMessage";
import { Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface EnhancedMenuSectionProps {
    // Optional props for backward compatibility
    items?: ItemDocument[];
}

const EnhancedMenuSection = ({ items: legacyItems }: EnhancedMenuSectionProps) => {
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [dietaryFilter, setDietaryFilter] = useState<string | null>(null);
    const [allergenFilter, setAllergenFilter] = useState<string | null>(null);
    const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

    const t = useTranslations();
    const { data: menuData, error, isFetching } = useMenu();

    if (isFetching) {
        return <Loading message={t('loading_menu')} />;
    }

    if (error) {
        return <ErrorMessage error={error.message} />;
    }

    if (!menuData) {
        return null;
    }

    const { menu, allergens } = menuData;

    const toggleCategory = (categoryId: string) => {
        const newExpanded = new Set(expandedCategories);
        if (newExpanded.has(categoryId)) {
            newExpanded.delete(categoryId);
        } else {
            newExpanded.add(categoryId);
        }
        setExpandedCategories(newExpanded);
    };

    const filterItems = (items: ItemDocument[]) => {
        return items.filter(item => {
            // Dietary filter
            if (dietaryFilter) {
                switch (dietaryFilter) {
                    case 'vegetarian':
                        if (!item.dietary?.vegetarian) return false;
                        break;
                    case 'vegan':
                        if (!item.dietary?.vegan) return false;
                        break;
                    case 'gluten_free':
                        if (!item.dietary?.gluten_free) return false;
                        break;
                    case 'dairy_free':
                        if (!item.dietary?.dairy_free) return false;
                        break;
                }
            }

            // Allergen filter
            if (allergenFilter && item.allergens?.includes(allergenFilter)) {
                return false;
            }

            return true;
        });
    };

    const categoriesToShow = selectedCategory 
        ? menu.categories.filter(cat => cat.id === selectedCategory)
        : menu.categories;

    return (
        <div className="w-full">
            {/* Filter Controls */}
            <div className="mb-6 p-4 bg-gray-50 rounded-xl">
                <div className="flex items-center gap-2 mb-3">
                    <Filter className="w-4 h-4 text-gray-600" />
                    <span className="font-medium text-gray-700">Filters</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Category Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Category
                        </label>
                        <select
                            value={selectedCategory || ''}
                            onChange={(e) => setSelectedCategory(e.target.value || null)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                            <option value="">All Categories</option>
                            {menu.categories.map(category => (
                                <option key={category.id} value={category.id}>
                                    {category.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Dietary Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Dietary
                        </label>
                        <select
                            value={dietaryFilter || ''}
                            onChange={(e) => setDietaryFilter(e.target.value || null)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                            <option value="">All Options</option>
                            <option value="vegetarian">Vegetarian</option>
                            <option value="vegan">Vegan</option>
                            <option value="gluten_free">Gluten Free</option>
                            <option value="dairy_free">Dairy Free</option>
                        </select>
                    </div>

                    {/* Allergen Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Allergen Free
                        </label>
                        <select
                            value={allergenFilter || ''}
                            onChange={(e) => setAllergenFilter(e.target.value || null)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                            <option value="">No Filter</option>
                            {Object.entries(allergens).map(([id, allergen]) => (
                                <option key={id} value={id}>
                                    {allergen.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            {/* Categories and Items */}
            <div className="space-y-6">
                {categoriesToShow.map((category) => {
                    const filteredItems = filterItems(category.items);
                    const isExpanded = expandedCategories.has(category.id);
                    
                    if (filteredItems.length === 0) return null;

                    return (
                        <div key={category.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            {/* Category Header */}
                            <div 
                                className="p-4 bg-gradient-to-r from-primary-50 to-primary-100 cursor-pointer hover:from-primary-100 hover:to-primary-150 transition-all duration-200"
                                onClick={() => toggleCategory(category.id)}
                            >
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h2 className="text-xl font-bold text-gray-900">{category.name}</h2>
                                        {category.description && (
                                            <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                                        )}
                                        {category.note && (
                                            <p className="text-xs text-orange-600 mt-1 font-medium">{category.note}</p>
                                        )}
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm text-gray-500">
                                            {filteredItems.length} items
                                        </span>
                                        {isExpanded ? (
                                            <ChevronUp className="w-5 h-5 text-gray-400" />
                                        ) : (
                                            <ChevronDown className="w-5 h-5 text-gray-400" />
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Category Items */}
                            {isExpanded && (
                                <div className="p-4">
                                    <div className="space-y-3">
                                        {filteredItems.map((item) => (
                                            <EnhancedItem
                                                key={item._id?.toString() || item.name}
                                                item={item}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>

            {categoriesToShow.every(cat => filterItems(cat.items).length === 0) && (
                <div className="text-center py-8">
                    <p className="text-gray-500">No items match your current filters.</p>
                </div>
            )}
        </div>
    );
};

export default EnhancedMenuSection;
