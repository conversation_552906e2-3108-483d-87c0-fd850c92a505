import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";
import { seedAllData } from "@/lib/seedData";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        
        console.log('Starting data seeding...');
        await seedAllData();
        
        return NextResponse.json({ 
            message: 'Data seeded successfully',
            timestamp: new Date().toISOString()
        });
    }).catch((err) => {
        console.error('Error seeding data:', err);
        return NextResponse.json(
            { message: 'There was an error seeding data', error: err.message },
            { status: 500 },
        );
    });
}
