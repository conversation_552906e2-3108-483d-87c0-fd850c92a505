import { CategoryModel } from "@/model/category";
import dbConnect from "@/lib/dbConnect";
import { requireAuth } from "@/lib/auth/serverAuth";
import { NextResponse } from "next/server";

const dbReady = dbConnect();

const withDB = async <T>(fn: () => Promise<T>) => {
    await dbReady;
    return fn();
};

export async function GET(request: Request) {
    return withDB(async () => {
        const categories = await CategoryModel.find({ enabled: true })
            .sort({ sort_order: 1 })
            .lean();
        return NextResponse.json(categories);
    }).catch((err) => {
        console.error('GET /categories error ➜', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}

export async function POST(request: Request) {
    return withDB(async () => {
        await requireAuth();
        const newCategory = await request.json();
        console.log('Creating category with data:', newCategory);
        
        const category = new CategoryModel(newCategory);
        await category.save();
        return NextResponse.json(category);
    }).catch((err) => {
        console.error('Error creating category:', err);
        return NextResponse.json(
            { message: 'There was an error on our side' },
            { status: 500 },
        );
    });
}
