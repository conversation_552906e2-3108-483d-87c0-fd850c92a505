import { Document, model, Model, Schema, Types } from "mongoose";

export interface Category {
    id: string;
    name: string;
    description?: string;
    note?: string;
    sort_order: number;
    enabled: boolean;
    createdAt?: Date;
}

// Extend the interface to include the MongoDB `_id` field
export interface CategoryDocument extends Category, Document {
    _id: Types.ObjectId;
}

// Define the schema for the Category model
export const categorySchema = new Schema<CategoryDocument>(
    {
        id: { type: String, required: true, unique: true },
        name: { type: String, required: true },
        description: { type: String },
        note: { type: String },
        sort_order: { type: Number, required: true, default: 0 },
        enabled: { type: Boolean, default: true },
        createdAt: { type: Date, default: Date.now },
    },
    {
        timestamps: true,
    }
);

categorySchema.index({ sort_order: 1 });
categorySchema.index({ id: 1 });

// Create the Category model
let CategoryModel: Model<CategoryDocument>;
try {
    CategoryModel = model<CategoryDocument>("Category");
} catch (error) {
    CategoryModel = model<CategoryDocument>("Category", categorySchema);
}

export { CategoryModel };
