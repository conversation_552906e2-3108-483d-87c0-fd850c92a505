'use client';

import React, { useState } from "react"; // Added useCallback, useMemo
import WithSystemCheck from "@/app/WithSystemCheck"; // Keep HOC wrapper
import IntroductionSection from '@/app/components/order/IntroductionSection';
import MenuSection from '@/app/components/order/MenuSection';
import EnhancedMenuSection from '@/app/components/order/EnhancedMenuSection';
import FloatingOrderSummary from '@/app/components/order/FloatingOrderSummary';
import OrderSummary from "@/app/components/order/OrderSummary";

import { useTranslations } from 'next-intl';
import { useItems, useMenu } from "@/lib/fetch/item";
import { Loading } from "@/app/components/Loading";
import ErrorMessage from "@/app/components/ErrorMessage";

const Page: React.FC = () => {
    const [ordersOpen, setOrdersOpen] = useState(false);
    const [useEnhancedMenu, setUseEnhancedMenu] = useState(true);
    const t = useTranslations();

    // Try to use the enhanced menu first, fallback to legacy if needed
    const { data: menuData, error: menuError, isFetching: menuFetching } = useMenu();
    const { data: legacyData, error: legacyError, isFetching: legacyFetching } = useItems();

    const isLoading = useEnhancedMenu ? menuFetching : legacyFetching;
    const error = useEnhancedMenu ? menuError : legacyError;
    const hasData = useEnhancedMenu ? !!menuData : !!legacyData;

    // If enhanced menu fails, fallback to legacy
    if (useEnhancedMenu && menuError && !menuFetching) {
        console.warn('Enhanced menu failed, falling back to legacy menu:', menuError);
        setUseEnhancedMenu(false);
    }

    if (isLoading) {
        return <Loading message={t('loading_menu')}/>
    }

    if (error) {
        return <ErrorMessage error={error.message}/>;
    }

    if (!hasData) {
        return null;
    }

    if (!ordersOpen) {
        return (
            <div className="max-w-6xl mx-auto space-y-6">
                <IntroductionSection/>

                <div className="mb-24">
                    {useEnhancedMenu && menuData ? (
                        <EnhancedMenuSection />
                    ) : (
                        <div className="bg-white p-6 md:p-8 rounded-2xl shadow-md">
                            <MenuSection items={legacyData || []} />
                        </div>
                    )}
                </div>

                <FloatingOrderSummary
                    onToggleOpen={() => setOrdersOpen(true)}
                />
            </div>
        )
    } else {
        return <OrderSummary onClose={() => setOrdersOpen(false)}/>
    }
};

export default WithSystemCheck(Page);
