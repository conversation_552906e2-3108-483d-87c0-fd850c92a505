import { Document, model, Model, Schema, Types } from "mongoose";

// Enhanced ingredient interface with category and origin
export interface Ingredient {
    name: string;
    category: 'cheese' | 'meat' | 'vegetable' | 'herb' | 'sauce' | 'specialty' | 'vegan_cheese' | 'plant_protein';
    origin?: string;
}

// Price structure with currency support
export interface Price {
    amount: number;
    currency: string;
}

// Size options for items
export interface SizeOption {
    size: string;
    price: number;
    description?: string;
}

// Dietary information with boolean flags
export interface DietaryInfo {
    vegetarian: boolean;
    vegan: boolean;
    gluten_free: boolean;
    dairy_free: boolean;
}

// Customization options
export interface CustomizationOptions {
    size_options?: SizeOption[];
    additional_toppings?: string[];
    removable_ingredients?: string[];
}

// Customization details for ordered items
export interface CustomizationDetails {
    size?: string;
    added_toppings?: Record<string, number>;
    removed_ingredients?: string[];
}

export interface Item {
    name: string;
    description?: string;
    price: Price;
    category_id: string;
    type: string;
    dietary: DietaryInfo;
    ingredients: Ingredient[];
    allergens: string[]; // References to allergen IDs
    size: number;
    enabled: boolean;
    customizable: boolean;
    customization_options?: CustomizationOptions;
    customization_details?: CustomizationDetails; // For ordered items with customizations
    specialty_ingredient?: string;
    premium?: boolean;
    signature_dish?: boolean;
    sort_order?: number;
    createdAt?: Date;
}

// Extend the interface to include the MongoDB `_id` field
export interface ItemDocument extends Item, Document {
    _id: Types.ObjectId;
}

// Schema for ingredient subdocument
const ingredientSchema = new Schema<Ingredient>({
    name: { type: String, required: true },
    category: {
        type: String,
        required: true,
        enum: ['cheese', 'meat', 'vegetable', 'herb', 'sauce', 'specialty', 'vegan_cheese', 'plant_protein']
    },
    origin: { type: String }
}, { _id: false });

// Schema for price subdocument
const priceSchema = new Schema<Price>({
    amount: { type: Number, required: true, min: 0, max: 999 },
    currency: { type: String, required: true, default: 'CHF' }
}, { _id: false });

// Schema for size options
const sizeOptionSchema = new Schema<SizeOption>({
    size: { type: String, required: true },
    price: { type: Number, required: true, min: 0 },
    description: { type: String }
}, { _id: false });

// Schema for dietary information
const dietaryInfoSchema = new Schema<DietaryInfo>({
    vegetarian: { type: Boolean, default: false },
    vegan: { type: Boolean, default: false },
    gluten_free: { type: Boolean, default: false },
    dairy_free: { type: Boolean, default: false }
}, { _id: false });

// Schema for customization options
const customizationOptionsSchema = new Schema<CustomizationOptions>({
    size_options: [sizeOptionSchema],
    additional_toppings: [String],
    removable_ingredients: [String]
}, { _id: false });

// Schema for customization details (for ordered items)
const customizationDetailsSchema = new Schema<CustomizationDetails>({
    size: { type: String },
    added_toppings: { type: Map, of: Number },
    removed_ingredients: [String]
}, { _id: false });

// Define the schema for the Item model
export const itemSchema = new Schema<ItemDocument>(
    {
        name: { type: String, required: true },
        description: { type: String },
        price: { type: priceSchema, required: true },
        category_id: { type: String, required: true },
        type: { type: String, required: true },
        dietary: { type: dietaryInfoSchema, required: true },
        ingredients: [ingredientSchema],
        allergens: [String], // Array of allergen IDs
        size: { type: Number, required: true, default: 1, min: 0.1, max: 1 },
        enabled: { type: Boolean, default: true },
        customizable: { type: Boolean, default: false },
        customization_options: customizationOptionsSchema,
        customization_details: customizationDetailsSchema,
        specialty_ingredient: { type: String },
        premium: { type: Boolean, default: false },
        signature_dish: { type: Boolean, default: false },
        sort_order: { type: Number, default: 0 },
        createdAt: { type: Date, default: Date.now },
    },
    {
        timestamps: true,
    }
);

itemSchema.index({ price: 1 });


// Create the Item model
let ItemModel: Model<ItemDocument>;
try {
    ItemModel = model<ItemDocument>("Item");
} catch (error) {
    ItemModel = model<ItemDocument>("Item", itemSchema);
}

export { ItemModel };
