import { AllergenModel } from "@/model/allergen";
import { CategoryModel } from "@/model/category";

// Allergen data from Quick Pizza system
export const allergenData = [
    {
        id: "1",
        name: "Gluten",
        description: "Glutenhaltiges Getreide (Weizen, Dinkel, Khorasan-Weizen, Kamut, Roggen, Gerste, Hafer)"
    },
    {
        id: "2",
        name: "<PERSON>",
        description: "Milch und daraus gewonnene Erzeugnisse (einschliesslich Laktose)"
    },
    {
        id: "3",
        name: "Egg<PERSON>",
        description: "Eier und daraus gewonnene Erzeugnisse"
    },
    {
        id: "4",
        name: "Soy",
        description: "Sojabohnen und daraus gewonnene Erzeugnisse"
    },
    {
        id: "5",
        name: "Peanuts",
        description: "Erdnüsse und daraus gewonnene Erzeugnisse"
    },
    {
        id: "6",
        name: "Tree nuts",
        description: "Hartschalenobst (Nüsse): <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>adamian<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Peca<PERSON><PERSON>sse, Pistazien"
    },
    {
        id: "7",
        name: "<PERSON><PERSON><PERSON>",
        description: "Sellerie und daraus gewonnene Erzeugnisse"
    },
    {
        id: "8",
        name: "Mustard",
        description: "Senf und daraus gewonnene Erzeugnisse"
    },
    {
        id: "9",
        name: "Sulfites",
        description: "Schwefeldioxid und Sulfite"
    },
    {
        id: "10",
        name: "Molluscs",
        description: "Weichtiere und daraus gewonnene Erzeugnisse"
    },
    {
        id: "11",
        name: "Fish",
        description: "Fische und daraus gewonnene Erzeugnisse"
    },
    {
        id: "12",
        name: "Crustaceans",
        description: "Krebstiere und daraus gewonnene Erzeugnisse"
    },
    {
        id: "13",
        name: "Sesame",
        description: "Sesamsamen und daraus gewonnene Erzeugnisse"
    },
    {
        id: "14",
        name: "Lupines",
        description: "Lupinen und daraus gewonnene Erzeugnisse"
    }
];

// Category data based on Quick Pizza system
export const categoryData = [
    {
        id: "dop_pizzas",
        name: "Le pizze DOP",
        description: "Denominazione di Origine Protetta - geschützter Herkunftssiegel",
        note: "keine Änderungen möglich",
        sort_order: 1
    },
    {
        id: "classic_pizzas",
        name: "Pizze",
        description: "Traditional Italian pizzas",
        sort_order: 2
    },
    {
        id: "vegan_pizzas",
        name: "Pizze vegane",
        description: "Vegan pizza options with plant-based cheese",
        sort_order: 3
    },
    {
        id: "pasta",
        name: "Pasta",
        description: "Traditional Italian pasta dishes",
        sort_order: 4
    },
    {
        id: "salads",
        name: "Salads",
        description: "Fresh salads and appetizers",
        sort_order: 5
    },
    {
        id: "beverages",
        name: "Beverages",
        description: "Drinks and beverages",
        sort_order: 6
    }
];

export async function seedAllergens() {
    try {
        // Clear existing allergens
        await AllergenModel.deleteMany({});
        
        // Insert new allergens
        await AllergenModel.insertMany(allergenData);
        console.log('Allergens seeded successfully');
    } catch (error) {
        console.error('Error seeding allergens:', error);
        throw error;
    }
}

export async function seedCategories() {
    try {
        // Clear existing categories
        await CategoryModel.deleteMany({});
        
        // Insert new categories
        await CategoryModel.insertMany(categoryData);
        console.log('Categories seeded successfully');
    } catch (error) {
        console.error('Error seeding categories:', error);
        throw error;
    }
}

export async function seedAllData() {
    await seedAllergens();
    await seedCategories();
}
