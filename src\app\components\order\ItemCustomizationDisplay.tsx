'use client'

import React from 'react';
import { ItemDocument } from '@/model/item';
import { Plus, Minus, ChefHat } from 'lucide-react';

interface ItemCustomizationDisplayProps {
    item: ItemDocument;
    showTitle?: boolean;
    compact?: boolean;
    preparationMode?: boolean; // Enhanced display for kitchen staff
}

interface CustomizationDetails {
    size?: string;
    added_toppings?: Record<string, number>;
    removed_ingredients?: string[];
}

const ItemCustomizationDisplay: React.FC<ItemCustomizationDisplayProps> = ({
    item,
    showTitle = true,
    compact = false,
    preparationMode = false
}) => {
    // Extract customization details from the item
    const customizationDetails = (item as any).customization_details as CustomizationDetails;

    if (!customizationDetails) {
        return null;
    }

    const { size, added_toppings, removed_ingredients } = customizationDetails;
    
    // Check if there are any customizations to display
    const hasCustomizations = size !== 'regular' || 
                             (added_toppings && Object.keys(added_toppings).length > 0) || 
                             (removed_ingredients && removed_ingredients.length > 0);

    if (!hasCustomizations) {
        return null;
    }

    if (compact) {
        return (
            <div className="text-xs text-gray-500 mt-1">
                {size && size !== 'regular' && (
                    <span className="inline-flex items-center gap-1 mr-2">
                        <ChefHat className="w-3 h-3" />
                        {size}
                    </span>
                )}
                {added_toppings && Object.keys(added_toppings).length > 0 && (
                    <span className="inline-flex items-center gap-1 mr-2">
                        <Plus className="w-3 h-3 text-green-600" />
                        {Object.keys(added_toppings).length} extra{Object.keys(added_toppings).length > 1 ? 's' : ''}
                    </span>
                )}
                {removed_ingredients && removed_ingredients.length > 0 && (
                    <span className="inline-flex items-center gap-1">
                        <Minus className="w-3 h-3 text-red-600" />
                        {removed_ingredients.length} removed
                    </span>
                )}
            </div>
        );
    }

    return (
        <div className={`mt-3 p-3 rounded-lg border ${
            preparationMode
                ? 'bg-yellow-50 border-yellow-200'
                : 'bg-gray-50 border-gray-100'
        }`}>
            {showTitle && (
                <div className="flex items-center gap-2 mb-2">
                    <ChefHat className={`w-4 h-4 ${preparationMode ? 'text-yellow-600' : 'text-gray-600'}`} />
                    <span className={`text-sm font-medium ${
                        preparationMode ? 'text-yellow-800' : 'text-gray-700'
                    }`}>
                        {preparationMode ? '🍕 SPECIAL INSTRUCTIONS' : 'Customizations'}
                    </span>
                </div>
            )}
            
            <div className="space-y-2">
                {/* Size customization */}
                {size && size !== 'regular' && (
                    <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                            preparationMode
                                ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
                                : 'bg-blue-100 text-blue-800'
                        }`}>
                            {preparationMode ? '📏 SIZE:' : 'Size:'} {size.toUpperCase()}
                        </span>
                    </div>
                )}

                {/* Added toppings */}
                {added_toppings && Object.keys(added_toppings).length > 0 && (
                    <div>
                        <div className="flex items-center gap-1 mb-1">
                            <Plus className="w-3 h-3 text-green-600" />
                            <span className={`text-xs font-medium ${
                                preparationMode ? 'text-green-800' : 'text-green-700'
                            }`}>
                                {preparationMode ? '➕ ADD THESE TOPPINGS:' : 'Added:'}
                            </span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                            {Object.entries(added_toppings).map(([topping, quantity]) => (
                                <span
                                    key={topping}
                                    className={`text-xs px-2 py-1 rounded-full font-medium ${
                                        preparationMode
                                            ? 'bg-green-200 text-green-900 border border-green-400'
                                            : 'bg-green-100 text-green-800'
                                    }`}
                                >
                                    {topping.toUpperCase()} {quantity > 1 ? `(×${quantity})` : ''}
                                </span>
                            ))}
                        </div>
                    </div>
                )}

                {/* Removed ingredients */}
                {removed_ingredients && removed_ingredients.length > 0 && (
                    <div>
                        <div className="flex items-center gap-1 mb-1">
                            <Minus className="w-3 h-3 text-red-600" />
                            <span className={`text-xs font-medium ${
                                preparationMode ? 'text-red-800' : 'text-red-700'
                            }`}>
                                {preparationMode ? '❌ DO NOT ADD:' : 'Removed:'}
                            </span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                            {removed_ingredients.map((ingredient) => (
                                <span
                                    key={ingredient}
                                    className={`text-xs px-2 py-1 rounded-full font-medium ${
                                        preparationMode
                                            ? 'bg-red-200 text-red-900 border border-red-400'
                                            : 'bg-red-100 text-red-800'
                                    }`}
                                >
                                    {ingredient.toUpperCase()}
                                </span>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ItemCustomizationDisplay;
